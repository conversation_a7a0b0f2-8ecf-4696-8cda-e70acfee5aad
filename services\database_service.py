"""
CogBridges Search - 数据库服务
实现PostgreSQL数据库的CRUD操作和数据迁移功能
"""

import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager

from config import config
from models.database_models import (
    Base, SearchSession, GoogleSearchResult, RedditPost, RedditComment,
    UserHistory, SubredditSimilarity, CommentMotivationAnalysis
)
from models.search_models import SearchResult, SearchQuery
from models.reddit_models import RedditPost as RedditPostModel, RedditComment as RedditCommentModel
from utils.logger_utils import get_logger


class DatabaseService:
    """数据库服务类"""
    
    def __init__(self):
        """初始化数据库服务"""
        self.logger = get_logger(__name__)
        self.engine = None
        self.SessionLocal = None
        
        if config.ENABLE_DATABASE and config.database_configured:
            try:
                self._initialize_database()
                self.logger.info("数据库服务初始化成功")
            except Exception as e:
                self.logger.error(f"数据库服务初始化失败: {e}")
                raise
        else:
            self.logger.warning("数据库未启用或未配置")
    
    def _initialize_database(self):
        """初始化数据库连接"""
        try:
            # 创建数据库引擎
            self.engine = create_engine(
                config.database_url,
                pool_size=config.DB_POOL_SIZE,
                max_overflow=config.DB_MAX_OVERFLOW,
                pool_timeout=config.DB_POOL_TIMEOUT,
                echo=config.DEBUG_MODE  # 在调试模式下显示SQL
            )
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            
            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self.logger.info("数据库连接建立成功")
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """获取数据库会话的上下文管理器"""
        if not self.SessionLocal:
            raise RuntimeError("数据库未初始化")
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def create_tables(self):
        """创建数据库表"""
        if not self.engine:
            raise RuntimeError("数据库引擎未初始化")
        
        try:
            Base.metadata.create_all(bind=self.engine)
            self.logger.info("数据库表创建成功")
        except Exception as e:
            self.logger.error(f"创建数据库表失败: {e}")
            raise
    
    def drop_tables(self):
        """删除数据库表（谨慎使用）"""
        if not self.engine:
            raise RuntimeError("数据库引擎未初始化")
        
        try:
            Base.metadata.drop_all(bind=self.engine)
            self.logger.warning("数据库表已删除")
        except Exception as e:
            self.logger.error(f"删除数据库表失败: {e}")
            raise
    
    def save_search_session(
        self,
        session_id: str,
        search_result: SearchResult,
        reddit_data: Optional[Dict[str, Any]] = None,
        raw_data: Optional[Dict[str, Any]] = None,
        llm_analysis: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        保存搜索会话数据

        Args:
            session_id: 会话ID
            search_result: 搜索结果对象
            reddit_data: Reddit数据
            raw_data: 原始JSON数据（用于备份）
            llm_analysis: LLM分析结果

        Returns:
            保存是否成功
        """
        try:
            with self.get_session() as session:
                # 创建搜索会话记录
                search_session = SearchSession(
                    id=session_id,
                    query=search_result.query.query,
                    timestamp=search_result.query.timestamp,
                    search_type=search_result.query.search_type,
                    max_results=search_result.query.max_results,
                    site_filter=search_result.query.site_filter,
                    success=search_result.success,
                    error_message=search_result.error_message,
                    raw_data=raw_data
                )

                # 添加LLM分析信息
                if llm_analysis:
                    search_session.llm_analysis_success = llm_analysis.get('success', False)
                    search_session.llm_analysis_time = llm_analysis.get('analysis_time', 0.0)
                    search_session.llm_analysis_error = llm_analysis.get('error')
                
                # 保存Google搜索结果
                if search_result.google_results:
                    for i, result in enumerate(search_result.google_results):
                        google_result = GoogleSearchResult(
                            session_id=session_id,
                            title=result.title,
                            url=result.url,
                            snippet=result.snippet,
                            display_url=result.display_url,
                            rank=i + 1
                        )
                        session.add(google_result)
                    
                    search_session.google_results_count = len(search_result.google_results)
                
                # 保存Reddit数据
                if reddit_data:
                    self._save_reddit_data(session, session_id, reddit_data, search_session)

                # 保存LLM分析数据
                if llm_analysis:
                    self._save_llm_analysis_data(session, session_id, llm_analysis)

                session.add(search_session)
                
            self.logger.info(f"搜索会话数据保存成功: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存搜索会话数据失败: {e}")
            return False
    
    def _save_reddit_data(
        self,
        session: Session,
        session_id: str,
        reddit_data: Dict[str, Any],
        search_session: SearchSession
    ):
        """保存Reddit数据到数据库"""
        posts_count = 0
        comments_count = 0
        
        # 保存Reddit帖子
        if 'reddit_posts' in reddit_data:
            for post_data in reddit_data['reddit_posts']:
                if isinstance(post_data, dict):
                    reddit_post = RedditPost(
                        id=post_data.get('id', ''),
                        session_id=session_id,
                        title=post_data.get('title', ''),
                        selftext=post_data.get('selftext', ''),
                        author=post_data.get('author', ''),
                        score=post_data.get('score', 0),
                        num_comments=post_data.get('num_comments', 0),
                        created_utc=post_data.get('created_utc', 0),
                        subreddit=post_data.get('subreddit', ''),
                        permalink=post_data.get('permalink', ''),
                        url=post_data.get('url', ''),
                        motivation_analysis=post_data.get('motivation_analysis')
                    )
                    session.add(reddit_post)
                    posts_count += 1
        
        # 保存Reddit评论
        if 'reddit_comments' in reddit_data:
            for comment_data in reddit_data['reddit_comments']:
                if isinstance(comment_data, dict):
                    reddit_comment = RedditComment(
                        id=comment_data.get('id', ''),
                        session_id=session_id,
                        post_id=comment_data.get('post_id'),
                        body=comment_data.get('body', ''),
                        author=comment_data.get('author', ''),
                        score=comment_data.get('score', 0),
                        created_utc=comment_data.get('created_utc', 0),
                        parent_id=comment_data.get('parent_id', ''),
                        subreddit=comment_data.get('subreddit', ''),
                        permalink=comment_data.get('permalink', ''),
                        motivation_analysis=comment_data.get('motivation_analysis')
                    )
                    session.add(reddit_comment)
                    comments_count += 1
        
        # 保存用户历史数据
        if 'user_histories' in reddit_data:
            for user_data in reddit_data['user_histories']:
                if isinstance(user_data, dict):
                    user_history = UserHistory(
                        session_id=session_id,
                        username=user_data.get('username', ''),
                        total_comments=user_data.get('total_comments', 0),
                        total_posts=user_data.get('total_posts', 0),
                        account_created_utc=user_data.get('account_created_utc'),
                        comments_data=user_data.get('comments_data'),
                        posts_data=user_data.get('posts_data')
                    )
                    session.add(user_history)
        
        # 更新统计信息
        search_session.reddit_posts_count = posts_count
        search_session.reddit_comments_count = comments_count

    def _save_llm_analysis_data(
        self,
        session: Session,
        session_id: str,
        llm_analysis: Dict[str, Any]
    ):
        """保存LLM分析数据到数据库"""
        try:
            # 保存子版块相似性分析结果
            similarity_analysis = llm_analysis.get('similarity_analysis', {})
            if similarity_analysis:
                for username, user_similarity in similarity_analysis.items():
                    if isinstance(user_similarity, dict):
                        subreddit_similarity = SubredditSimilarity(
                            session_id=session_id,
                            username=username,
                            target_subreddits=user_similarity.get('target_subreddits', []),
                            user_subreddits=user_similarity.get('user_subreddits', []),
                            similarity_results=user_similarity.get('similarity_results', {}),
                            all_similar_subreddits=user_similarity.get('all_similar_subreddits', []),
                            analysis_success=user_similarity.get('success', True),
                            analysis_error=user_similarity.get('error'),
                            llm_response_raw=user_similarity.get('llm_response_raw')
                        )
                        session.add(subreddit_similarity)

            # 保存评论动机分析结果
            motivation_analysis = llm_analysis.get('motivation_analysis', {})
            if motivation_analysis:
                for username, user_motivations in motivation_analysis.items():
                    if isinstance(user_motivations, list):
                        for motivation_data in user_motivations:
                            if isinstance(motivation_data, dict):
                                comment_motivation = CommentMotivationAnalysis(
                                    session_id=session_id,
                                    username=username,
                                    comment_id=motivation_data.get('comment_id'),
                                    target_subreddit=motivation_data.get('target_subreddit', ''),
                                    professional_background=motivation_data.get('professional_background'),
                                    participation_motivation=motivation_data.get('participation_motivation'),
                                    interest_areas=motivation_data.get('interest_areas'),
                                    user_profile=motivation_data.get('user_profile'),
                                    matching_value=motivation_data.get('matching_value'),
                                    overall_assessment=motivation_data.get('overall_assessment'),
                                    analysis_success=motivation_data.get('success', True),
                                    analysis_error=motivation_data.get('error'),
                                    llm_response_raw=motivation_data.get('raw_analysis'),
                                    target_post_data=motivation_data.get('target_post_data'),
                                    user_comment_data=motivation_data.get('user_comment_data'),
                                    similar_subreddits_data=motivation_data.get('similar_subreddits_data'),
                                    user_overview_data=motivation_data.get('user_overview_data')
                                )
                                session.add(comment_motivation)

            self.logger.info(f"LLM分析数据保存成功: {session_id}")

        except Exception as e:
            self.logger.error(f"保存LLM分析数据失败: {e}")
            # 不抛出异常，允许其他数据继续保存

    def _load_llm_analysis_data(self, session: Session, session_id: str) -> Optional[Dict[str, Any]]:
        """从数据库加载LLM分析数据"""
        try:
            llm_analysis = {}

            # 加载子版块相似性分析
            subreddit_similarities = session.query(SubredditSimilarity).filter(
                SubredditSimilarity.session_id == session_id
            ).all()

            if subreddit_similarities:
                similarity_analysis = {}
                for similarity in subreddit_similarities:
                    similarity_analysis[similarity.username] = similarity.to_dict()
                llm_analysis['similarity_analysis'] = similarity_analysis

            # 加载评论动机分析
            motivation_analyses = session.query(CommentMotivationAnalysis).filter(
                CommentMotivationAnalysis.session_id == session_id
            ).all()

            if motivation_analyses:
                motivation_analysis = {}
                for motivation in motivation_analyses:
                    username = motivation.username
                    if username not in motivation_analysis:
                        motivation_analysis[username] = []
                    motivation_analysis[username].append(motivation.to_dict())
                llm_analysis['motivation_analysis'] = motivation_analysis

            return llm_analysis if llm_analysis else None

        except Exception as e:
            self.logger.error(f"加载LLM分析数据失败: {e}")
            return None

    def load_search_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        加载搜索会话数据

        Args:
            session_id: 会话ID

        Returns:
            会话数据字典
        """
        try:
            with self.get_session() as session:
                # 查询搜索会话
                search_session = session.query(SearchSession).filter(
                    SearchSession.id == session_id
                ).first()

                if not search_session:
                    self.logger.warning(f"未找到会话: {session_id}")
                    return None

                # 构建完整的会话数据
                session_data = search_session.to_dict()

                # 加载Google搜索结果
                google_results = session.query(GoogleSearchResult).filter(
                    GoogleSearchResult.session_id == session_id
                ).order_by(GoogleSearchResult.rank).all()

                session_data['google_results'] = [result.to_dict() for result in google_results]

                # 加载Reddit帖子
                reddit_posts = session.query(RedditPost).filter(
                    RedditPost.session_id == session_id
                ).all()

                session_data['reddit_posts'] = [post.to_dict() for post in reddit_posts]

                # 加载Reddit评论
                reddit_comments = session.query(RedditComment).filter(
                    RedditComment.session_id == session_id
                ).all()

                session_data['reddit_comments'] = [comment.to_dict() for comment in reddit_comments]

                # 加载用户历史
                user_histories = session.query(UserHistory).filter(
                    UserHistory.session_id == session_id
                ).all()

                session_data['user_histories'] = [history.to_dict() for history in user_histories]

                # 加载LLM分析数据
                llm_analysis_data = self._load_llm_analysis_data(session, session_id)
                if llm_analysis_data:
                    session_data['llm_analysis'] = llm_analysis_data

                self.logger.info(f"会话数据加载成功: {session_id}")
                return session_data

        except Exception as e:
            self.logger.error(f"加载会话数据失败: {e}")
            return None

    def list_search_sessions(self, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        列出搜索会话

        Args:
            limit: 返回数量限制
            offset: 偏移量

        Returns:
            会话信息列表
        """
        try:
            with self.get_session() as session:
                sessions = session.query(SearchSession).order_by(
                    SearchSession.created_at.desc()
                ).offset(offset).limit(limit).all()

                return [s.to_dict() for s in sessions]

        except Exception as e:
            self.logger.error(f"列出会话失败: {e}")
            return []

    def delete_search_session(self, session_id: str) -> bool:
        """
        删除搜索会话数据

        Args:
            session_id: 会话ID

        Returns:
            删除是否成功
        """
        try:
            with self.get_session() as session:
                # 查询会话
                search_session = session.query(SearchSession).filter(
                    SearchSession.id == session_id
                ).first()

                if not search_session:
                    self.logger.warning(f"未找到会话: {session_id}")
                    return False

                # 删除会话（级联删除相关数据）
                session.delete(search_session)

                self.logger.info(f"会话数据删除成功: {session_id}")
                return True

        except Exception as e:
            self.logger.error(f"删除会话数据失败: {e}")
            return False

    def get_database_statistics(self) -> Dict[str, Any]:
        """
        获取数据库统计信息

        Returns:
            数据库统计信息字典
        """
        try:
            with self.get_session() as session:
                # 统计各表记录数
                sessions_count = session.query(SearchSession).count()
                google_results_count = session.query(GoogleSearchResult).count()
                reddit_posts_count = session.query(RedditPost).count()
                reddit_comments_count = session.query(RedditComment).count()
                user_histories_count = session.query(UserHistory).count()
                subreddit_similarities_count = session.query(SubredditSimilarity).count()
                motivation_analyses_count = session.query(CommentMotivationAnalysis).count()

                # 获取最新会话时间
                latest_session = session.query(SearchSession).order_by(
                    SearchSession.created_at.desc()
                ).first()

                latest_session_time = None
                if latest_session:
                    latest_session_time = latest_session.created_at.isoformat()

                return {
                    "database_enabled": config.ENABLE_DATABASE,
                    "database_configured": config.database_configured,
                    "sessions_count": sessions_count,
                    "google_results_count": google_results_count,
                    "reddit_posts_count": reddit_posts_count,
                    "reddit_comments_count": reddit_comments_count,
                    "user_histories_count": user_histories_count,
                    "subreddit_similarities_count": subreddit_similarities_count,
                    "motivation_analyses_count": motivation_analyses_count,
                    "latest_session_time": latest_session_time,
                    "database_url_configured": bool(config.DATABASE_URL)
                }

        except Exception as e:
            self.logger.error(f"获取数据库统计失败: {e}")
            return {
                "database_enabled": config.ENABLE_DATABASE,
                "database_configured": config.database_configured,
                "error": str(e)
            }

    def migrate_json_to_database(self, json_file_path: str) -> bool:
        """
        将JSON文件数据迁移到数据库

        Args:
            json_file_path: JSON文件路径

        Returns:
            迁移是否成功
        """
        try:
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取会话ID（从文件名或数据中）
            session_id = data.get('session_id')
            if not session_id:
                # 从文件名提取
                import os
                filename = os.path.basename(json_file_path)
                if filename.startswith('complete_session_'):
                    session_id = filename.replace('complete_session_', '').replace('.json', '')
                else:
                    self.logger.error(f"无法确定会话ID: {json_file_path}")
                    return False

            # 检查会话是否已存在
            with self.get_session() as session:
                existing = session.query(SearchSession).filter(
                    SearchSession.id == session_id
                ).first()

                if existing:
                    self.logger.warning(f"会话已存在，跳过迁移: {session_id}")
                    return True

            # 构建搜索结果对象
            search_result_data = data.get('search_result', {})
            query_data = search_result_data.get('query', {})

            search_query = SearchQuery(
                query=query_data.get('query', ''),
                timestamp=datetime.fromisoformat(query_data.get('timestamp', datetime.now().isoformat())),
                search_type=query_data.get('search_type', 'reddit'),
                max_results=query_data.get('max_results', 5),
                site_filter=query_data.get('site_filter', 'site:reddit.com')
            )

            search_result = SearchResult(
                query=search_query,
                success=search_result_data.get('success', True),
                error_message=search_result_data.get('error_message')
            )

            # 添加Google搜索结果
            if 'google_results' in search_result_data:
                from models.search_models import GoogleSearchResult as GoogleSearchResultModel
                search_result.google_results = []
                for result_data in search_result_data['google_results']:
                    google_result = GoogleSearchResultModel(
                        title=result_data.get('title', ''),
                        url=result_data.get('url', ''),
                        snippet=result_data.get('snippet', ''),
                        display_url=result_data.get('display_url', ''),
                        rank=result_data.get('rank', 0)
                    )
                    search_result.google_results.append(google_result)

            # 保存到数据库
            reddit_data = data.get('reddit_data', {})
            success = self.save_search_session(
                session_id=session_id,
                search_result=search_result,
                reddit_data=reddit_data,
                raw_data=data
            )

            if success:
                self.logger.info(f"JSON数据迁移成功: {json_file_path}")
            else:
                self.logger.error(f"JSON数据迁移失败: {json_file_path}")

            return success

        except Exception as e:
            self.logger.error(f"迁移JSON数据失败: {e}")
            return False

    def is_available(self) -> bool:
        """检查数据库服务是否可用"""
        return (config.ENABLE_DATABASE and
                config.database_configured and
                self.engine is not None and
                self.SessionLocal is not None)
