-- 添加收藏表的数据库迁移脚本
-- 执行时间：2025-01-27

-- 创建收藏表
CREATE TABLE IF NOT EXISTS bookmarks (
    id VARCHAR(50) PRIMARY KEY,
    session_id VARCHAR(50) NOT NULL,
    comment_id VARCHAR(20),
    query TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    author VARCHAR(100) NOT NULL,
    subreddit VARCHAR(100),
    score INTEGER DEFAULT 0,
    url TEXT,
    tags JSON,
    category VARCHAR(50),
    bookmarked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_bookmarks_session 
        FOREIGN KEY (session_id) 
        REFERENCES search_sessions(id) 
        ON DELETE CASCADE,
    
    CONSTRAINT fk_bookmarks_comment 
        FOREIGN KEY (comment_id) 
        REFERENCES reddit_comments(id) 
        ON DELETE SET NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_bookmarks_session_id ON bookmarks(session_id);
CREATE INDEX IF NOT EXISTS idx_bookmarks_author ON bookmarks(author);
CREATE INDEX IF NOT EXISTS idx_bookmarks_subreddit ON bookmarks(subreddit);
CREATE INDEX IF NOT EXISTS idx_bookmarks_bookmarked_at ON bookmarks(bookmarked_at DESC);
CREATE INDEX IF NOT EXISTS idx_bookmarks_category ON bookmarks(category);

-- 添加更新时间戳触发器（PostgreSQL）
CREATE OR REPLACE FUNCTION update_bookmarks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_bookmarks_updated_at
    BEFORE UPDATE ON bookmarks
    FOR EACH ROW
    EXECUTE FUNCTION update_bookmarks_updated_at();

-- 添加注释
COMMENT ON TABLE bookmarks IS '用户收藏表';
COMMENT ON COLUMN bookmarks.id IS '收藏唯一标识';
COMMENT ON COLUMN bookmarks.session_id IS '关联的搜索会话ID';
COMMENT ON COLUMN bookmarks.comment_id IS '关联的Reddit评论ID';
COMMENT ON COLUMN bookmarks.query IS '原始搜索查询';
COMMENT ON COLUMN bookmarks.title IS '收藏标题';
COMMENT ON COLUMN bookmarks.content IS '收藏内容';
COMMENT ON COLUMN bookmarks.author IS 'Reddit用户名';
COMMENT ON COLUMN bookmarks.subreddit IS '子版块名称';
COMMENT ON COLUMN bookmarks.score IS '评论分数';
COMMENT ON COLUMN bookmarks.url IS '原始链接';
COMMENT ON COLUMN bookmarks.tags IS '标签列表（JSON格式）';
COMMENT ON COLUMN bookmarks.category IS '收藏分类';
COMMENT ON COLUMN bookmarks.bookmarked_at IS '收藏时间';
COMMENT ON COLUMN bookmarks.created_at IS '创建时间';
COMMENT ON COLUMN bookmarks.updated_at IS '更新时间';
